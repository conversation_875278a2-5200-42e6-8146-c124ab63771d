import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getUserInfo } from '@/api/auth'
import { useMenuStore } from './menu'

export const useUserStore = defineStore('user', () => {
  // 状态 - 使用ref
  const id = ref(null)
  const username = ref('')
  const email = ref('')
  const mp_open_id = ref(null)
  const roles = ref([])
  const token = ref('')

  // 计算属性 - 使用computed
  const isLoggedIn = computed(() => !!token.value)

  const userInfo = computed(() => ({
    id: id.value,
    username: username.value,
    email: email.value,
    mp_open_id: mp_open_id.value,
    roles: roles.value
  }))

  const authHeader = computed(() =>
    token.value ? `Bearer ${token.value}` : ''
  )

  // 方法 - 普通函数
  function setUserInfo(userInfo) {
    id.value = userInfo.id
    username.value = userInfo.username
    email.value = userInfo.email || ''
    mp_open_id.value = userInfo.mp_open_id || null
    roles.value = userInfo.roles || []
    if (userInfo.token) {
      token.value = userInfo.token
    }
  }

  function setToken(newToken) {
    token.value = newToken
  }

  function logout() {
    id.value = null
    username.value = ''
    email.value = ''
    mp_open_id.value = null
    roles.value = []
    token.value = ''
  }

  function updateUsername(newUsername) {
    username.value = newUsername
  }

  function updateEmail(newEmail) {
    email.value = newEmail
  }

  function clearUserData() {
    id.value = null
    username.value = ''
    email.value = ''
    mp_open_id.value = null
    roles.value = []
    token.value = ''
  }

  // 获取用户信息和菜单
  async function fetchUserInfo() {
    try {
      if (!token.value) {
        throw new Error('未登录')
      }

      const response = await getUserInfo()
      const { user, roles: userRoles, menus } = response.data

      // 更新用户信息
      setUserInfo({
        ...user,
        roles: userRoles
      })

      // 更新菜单信息
      const menuStore = useMenuStore()
      menuStore.setMenus(menus)

      return { user, roles: userRoles, menus }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，清除本地数据
      logout()
      const menuStore = useMenuStore()
      menuStore.clearMenus()
      throw error
    }
  }

  function logout() {
    id.value = null
    username.value = ''
    email.value = ''
    mp_open_id.value = null
    roles.value = []
    token.value = ''

    // 清除菜单数据
    const menuStore = useMenuStore()
    menuStore.clearMenus()
  }

  // 返回所有需要暴露的状态和方法
  return {
    // 状态
    id,
    username,
    email,
    mp_open_id,
    roles,
    token,
    // 计算属性
    isLoggedIn,
    userInfo,
    authHeader,
    // 方法
    setUserInfo,
    setToken,
    logout,
    updateUsername,
    updateEmail,
    clearUserData
  }
}, {
  // 持久化配置
  persist: {
    key: 'mailcode-user',
    storage: localStorage,
    paths: ['id', 'username', 'email', 'mp_open_id', 'roles', 'token'] // 持久化用户信息
  }
})
