import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useMenuStore = defineStore('menu', () => {
  // 状态
  const menus = ref([])
  const isMenuLoaded = ref(false)

  // 计算属性 - 获取扁平化的菜单路径列表
  const menuPaths = computed(() => {
    const paths = []
    
    const extractPaths = (menuList) => {
      menuList.forEach(menu => {
        // 根据菜单名称生成路径
        const path = generatePathFromName(menu.name)
        if (path) {
          paths.push(path)
        }
        
        // 递归处理子菜单
        if (menu.children && menu.children.length > 0) {
          extractPaths(menu.children)
        }
      })
    }
    
    extractPaths(menus.value)
    return paths
  })

  // 计算属性 - 获取导航菜单（用于Header显示）
  const navMenus = computed(() => {
    return menus.value.filter(menu => !menu.is_hidden)
  })

  // 根据菜单名称生成路径的映射
  const generatePathFromName = (name) => {
    const pathMap = {
      'Home': '/',
      'GetMailbox': '/get-mailbox',
      'MyMailbox': '/my-mailbox',
      'Contact': '/contact'
    }
    return pathMap[name] || null
  }

  // 方法
  function setMenus(menuList) {
    menus.value = menuList || []
    isMenuLoaded.value = true
  }

  function clearMenus() {
    menus.value = []
    isMenuLoaded.value = false
  }

  // 检查路径是否在用户菜单权限内
  function hasMenuPermission(path) {
    // 静态路由（登录、注册）始终允许访问
    const staticRoutes = ['/login', '/register']
    if (staticRoutes.includes(path)) {
      return true
    }

    // 检查是否在用户菜单权限内
    return menuPaths.value.includes(path)
  }

  // 根据路径查找菜单项
  function findMenuByPath(path) {
    const findInMenus = (menuList) => {
      for (const menu of menuList) {
        const menuPath = generatePathFromName(menu.name)
        if (menuPath === path) {
          return menu
        }
        
        if (menu.children && menu.children.length > 0) {
          const found = findInMenus(menu.children)
          if (found) return found
        }
      }
      return null
    }
    
    return findInMenus(menus.value)
  }

  return {
    // 状态
    menus,
    isMenuLoaded,
    // 计算属性
    menuPaths,
    navMenus,
    // 方法
    setMenus,
    clearMenus,
    hasMenuPermission,
    findMenuByPath,
    generatePathFromName
  }
}, {
  // 持久化配置
  persist: {
    key: 'mailcode-menu',
    storage: localStorage,
    paths: ['menus', 'isMenuLoaded']
  }
})
